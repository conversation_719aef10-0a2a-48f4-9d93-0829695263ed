# منصة التعليم - إعدادات Apache
# School Platform - Apache Configuration

# تفعيل محرك إعادة الكتابة
RewriteEngine On

# منع الوصول للملفات الحساسة
<Files "*.sql">
    Order allow,deny
    Deny from all
</Files>

<Files "*.log">
    Order allow,deny
    Deny from all
</Files>

<Files ".env">
    Order allow,deny
    Deny from all
</Files>

# حماية ملفات التكوين
<FilesMatch "^(config|database)\.php$">
    Order allow,deny
    Deny from all
</FilesMatch>

# منع عرض محتويات المجلدات
Options -Indexes

# تحسين الأمان - إخفاء معلومات الخادم
ServerTokens Prod
ServerSignature Off

# حماية من هجمات XSS
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set Permissions-Policy "geolocation=(), microphone=(), camera=()"
</IfModule>

# ضغط الملفات لتحسين الأداء
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# تخزين مؤقت للملفات الثابتة
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/ico "access plus 1 year"
    ExpiresByType image/icon "access plus 1 year"
    ExpiresByType text/plain "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
</IfModule>

# إعدادات رفع الملفات
php_value upload_max_filesize 10M
php_value post_max_size 10M
php_value max_execution_time 300
php_value max_input_time 300

# تفعيل التخزين المؤقت
<IfModule mod_headers.c>
    <FilesMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg)$">
        Header set Cache-Control "max-age=2592000, public"
    </FilesMatch>
</IfModule>

# إعادة توجيه الروابط الودية (إذا لزم الأمر)
# RewriteRule ^level/([0-9]+)/?$ level.php?id=$1 [L,QSA]
# RewriteRule ^subject/([0-9]+)/?$ subject.php?id=$1 [L,QSA]
# RewriteRule ^document/([0-9]+)/?$ view_document.php?id=$1 [L,QSA]

# منع الوصول المباشر لمجلد uploads (أمان إضافي)
<IfModule mod_rewrite.c>
    RewriteCond %{REQUEST_URI} ^/uploads/.*$
    RewriteCond %{HTTP_REFERER} !^https?://localhost [NC]
    RewriteCond %{HTTP_REFERER} !^https?://127.0.0.1 [NC]
    RewriteRule ^uploads/ - [F,L]
</IfModule>

# حماية من هجمات الحقن
<IfModule mod_rewrite.c>
    RewriteCond %{QUERY_STRING} (\<|%3C).*script.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} GLOBALS(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} _REQUEST(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*iframe.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*object.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*embed.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (SELECT|UNION|INSERT|DROP|DELETE|UPDATE|CREATE|ALTER|EXEC) [NC]
    RewriteRule ^(.*)$ - [F,L]
</IfModule>

# تحديد أنواع الملفات المسموحة للرفع
<FilesMatch "\.(php|php3|php4|php5|phtml|pl|py|jsp|asp|sh|cgi)$">
    <IfModule mod_dir.c>
        DirectoryIndex disabled
    </IfModule>
</FilesMatch>

# منع تنفيذ PHP في مجلد uploads
<Directory "uploads">
    <Files "*.php">
        Order allow,deny
        Deny from all
    </Files>
    <Files "*.phtml">
        Order allow,deny
        Deny from all
    </Files>
    <Files "*.php3">
        Order allow,deny
        Deny from all
    </Files>
    <Files "*.php4">
        Order allow,deny
        Deny from all
    </Files>
    <Files "*.php5">
        Order allow,deny
        Deny from all
    </Files>
</Directory>

# تحسين الأداء - ضغط gzip
<IfModule mod_gzip.c>
    mod_gzip_on Yes
    mod_gzip_dechunk Yes
    mod_gzip_item_include file \.(html?|txt|css|js|php|pl)$
    mod_gzip_item_include mime ^application/x-javascript.*
    mod_gzip_item_include mime ^text/.*
    mod_gzip_item_exclude rspheader ^Content-Encoding:.*gzip.*
    mod_gzip_item_exclude mime ^image/.*
    mod_gzip_item_include handler ^cgi-script$
</IfModule>

# إعدادات الترميز
AddDefaultCharset UTF-8

# منع hotlinking للصور
<IfModule mod_rewrite.c>
    RewriteCond %{HTTP_REFERER} !^$
    RewriteCond %{HTTP_REFERER} !^https?://localhost [NC]
    RewriteCond %{HTTP_REFERER} !^https?://127.0.0.1 [NC]
    RewriteCond %{HTTP_REFERER} !^https?://.*\.yourdomain\.com [NC]
    RewriteRule \.(jpg|jpeg|png|gif|bmp)$ - [F,NC]
</IfModule>

# صفحات الأخطاء المخصصة
ErrorDocument 404 /school-platform/404.php
ErrorDocument 403 /school-platform/403.php
ErrorDocument 500 /school-platform/500.php

# تحسين الذاكرة
php_value memory_limit 128M

# إعدادات الجلسات
php_value session.cookie_httponly 1
php_value session.use_only_cookies 1
php_value session.cookie_secure 0
php_value session.gc_maxlifetime 3600

# منع عرض أخطاء PHP في الإنتاج
# php_flag display_errors Off
# php_flag log_errors On
# php_value error_log /path/to/error.log
